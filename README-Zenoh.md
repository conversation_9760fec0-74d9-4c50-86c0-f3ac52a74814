# Система обмена данными через Zenoh

> **Цель** — дать разработчику быстрый и практичный способ получать телеметрию в РМО и
> отправлять команды на борт, не сильно углубляясь в сетевые детали.

Zenoh — broker‑less шина данных от Eclipse: Pub/Sub + Query + Storage в
одном протоколе, сотни тысяч сообщений/с при RTT <1 мс, zero‑copy внутри
процесса и мгновенное авто‑bridge DDS ↔ MQTT при необходимости. Для
встраиваемых систем это даёт: минимум зависимостей, гибкую маршрутизацию и
работу без «центрального сервера».

В проекте Zenoh заменяет ROS 2 DDS‑уровень: мы шлём ROS‑совместимые CDR‑пакеты,
но без огромного стек‑овер‑DDS‑RTPS.

Документ описывает, как мы интегрируем Zenoh в РМО с использованием двух крупных абстракции (клиент и
базовый виджет) и как быстро писать UI‑модули на их основе.

README состоит из четырёх блоков:

1. ***Архитектура и фишки реализации***
2. ***ZenohClient*** — сетевой уровень
3. ***ZenohWidget*** — базовый класс виджетов
4. ***Пример создания нового виджета***

---

## 1. Архитектура

| Блок            | Что делает                                                               | Ключевые особенности                                                                                                                                            |
| --------------- | ------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **ZenohClient** | Единственный объект, открывающий сессию Zenoh и работающий с ROS2‑типами | • Дедупликация подписок: на каждый `key_expr` ровно *один* native‑subscriber, сколько бы виджетов ни слушало. <br>• Точечная маршрутизация: каждый виджет получает только свои данные (`_routing[key_expr] → [callbacks]`) <br>• Единый `QThreadPool` <br>• сервисы и чтение storage    <br> `rosbags.typesys` динамически регистрирует .msg/.srv                                       |
| **ZenohWidget** | Базовый класс для PyQt‑виджетов                                          | • Автоподписка по конфигу <br>• Таймаут свежести данных <br>• Автопереподписка при смене namespace <br>• Сигналы `data_updated`, `data_freshness_changed` |


* **Экономятся сеть и CPU** : один пакет десериализуется ровно один раз, сколько бы виджетов ни слушало топик.
* **Код виджета короче**: подключаем `ZenohWidget` и пишем пару обработчиков, всё остальное (отписки, таймеры, namespace) автоматизировано.




---

## 2. ZenohClient

`ZenohClient` — единственный объект, открывающий `zenoh.Session` и умеющий
работать с ROS2‑сообщениями.

```python
from core.zenoh_client import ZenohClient
client = ZenohClient(core, msg_dirs=["…/drill_msgs/msg"])
```


### 2.1 Краткая таблица API

| Задача                  | Один‑лайнер                                                                                             |
| ----------------------- | ------------------------------------------------------------------------------------------------------- |
| **Подписка на стрим** данных        | `hdl = client.subscribe("level", "drill_msgs/msg/Level", callback=on_msg)`                              |
| **Остановка стрима**           | `client.unsubscribe(hdl)`                                                                               |
| **Публикация**          | `client.publish("tower_cmd", "drill_msgs/msg/TowerCtrl", tilt=0.3)`                                     |
| **Чтение поля из хранилища** (sync)  | `lat = client.read_field("gnss_fix", "sensor_msgs/msg/NavSatFix", "latitude")`                          |
| **Чтение поля** (async) | `client.get_field(...); client.fieldValueReceived.connect(slot)`                                        |
| **Вызов сервиса** (sync)       | `client.call_service("/turtle/teleport_relative", "turtlesim", "TeleportRelative", wait_response=True)` |
| **Вызов сервиса** (async)      | `client.call_service_async(...); client.serviceResponseReceived.connect(slot)`                          |

Все тяжёлые операции делаются в общем `QThreadPool`, ничего не блокирует GUI.


### 2.2 Конфигурация клиента

```jsonc
"zenoh_client": {
  "is_active": true,
  "file_name": "zenoh_client",
  "class_name": "ZenohClient",
  "init_args": { "msg_dirs": "/path/to/drill_msgs/msg" },
  "prep_args": {}
}
```


### Механизм подписок и маршрутизации в `ZenohClient`

Когда потребитель (например, `ZenohWidget` или любой другой код) вызывает `zenoh_client.subscribe(key_expr, msg_type, callback)`:

1.  **Разрешение Namespace и Ключа:** `ZenohClient` определяет полный ключ подписки, комбинируя `key_expr` с текущим `namespace` (если он есть).
2.  **Дедупликация Подписчиков:** Клиент проверяет, существует ли уже *нативная* Zenoh-подписка на этот `full_key`.
    *   Если **нет**, создается *один* `session.declare_subscriber(full_key, internal_listener)`. Этот `internal_listener` будет получать сырые данные от Zenoh.
    *   Если **да**, новый нативный подписчик не создается.
3.  **Регистрация Колбэка:** Пользовательский `callback` добавляется в внутренний список `_routing[full_key]`. Таким образом, на один `full_key` может быть подписано несколько колбэков из разных частей приложения.
4.  **Приём и Десериализация Пакета:** Когда Zenoh доставляет сообщение по `full_key`, срабатывает `internal_listener` клиента. Он:
    *   Десериализует CDR-байтстрим в Python-объект сообщения ROS2 (типа `msg_type`) с помощью `rosbags.typesys`. Это происходит *один раз* на пришедший пакет.
5.  **Маршрутизация Объекта Колбэкам:** Десериализованный Python-объект передается каждому `callback` из списка `_routing[full_key]`. Для GUI-компонентов это обычно происходит через `Qt.QueuedConnection`, чтобы вызов колбэка произошел в основном потоке GUI.
6.  **Отписка:** Когда потребитель вызывает `zenoh_client.unsubscribe(handle)`, соответствующий `callback` удаляется из `_routing[full_key]`. Если список колбэков для `full_key` становится пустым, нативная Zenoh-подписка (`_subscribers[full_key]`) также отменяется (`undeclare()`).

**Итог:** Такая схема обеспечивает, что на каждый уникальный топик (с учетом namespace) создается только одна низкоуровневая подписка Zenoh, и каждое пришедшее сообщение десериализуется только один раз, независимо от количества конечных потребителей этого сообщения в приложении.


---

## 3. ZenohWidget — базовый класс для виджетов

### 3.1 Что он делает

* **Связывает** конфиг‑JSON с реальной подпиской.
* **Группирует** источники: одна подписка на топик — несколько полей.
* **Мониторит** свежесть (`data_timeout`, по умолчанию 5 с).
* **Переключает** namespace при сигнале `core.vehicle_changed`.
* **Защищает** от утечек: хранит `SubscriptionHandle`‑ы, отписывается в `cleanup()`.

### 3.2 Основные атрибуты/сигналы

| Имя                                           | Тип       | Для чего                                                    |
| --------------------------------------------- | --------- | ----------------------------------------------------------- |
| `data_updated(str src, object value)`         | Qt‑сигнал | Посылается при каждом новом значении.                       |
| `data_freshness_changed(str src, bool fresh)` | Qt‑сигнал | Сработает, если источник затих более чем на `data_timeout`. |
| `data_sources`                                | dict      | Копия вашего JSON с описанием источников.                   |

### 3.3 Жизненный цикл и обработка данных в `ZenohWidget`

`ZenohWidget` использует `ZenohClient` для получения данных и предоставляет более высокоуровневый интерфейс для GUI-компонентов:

| Этап                                  | Что происходит                                                                                                                                                                                                                            |
| ------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1. Конфигурация (`prepare()`)**     | Виджет получает конфигурацию `data_sources`. Каждая запись в `data_sources` (например, `"pitch_angle"`) описывает, из какого Zenoh-топика (`topic`), какого типа сообщения (`msg_type`), какого поля этого сообщения (`field`) и с каким множителем (`multiplier`, опционально) нужно извлечь значение. |
| **2. Группировка и Подписка (`subscribe()`)** | При вызове `subscribe()` (обычно из `start()` или при смене `namespace`), `ZenohWidget`: <br> 1. Группирует все `data_sources` по уникальной паре `(topic, msg_type)`. <br> 2. Для каждой такой уникальной группы вызывает `self.zenoh.subscribe(topic, msg_type, self.current_namespace, callback=widget_internal_handler)`. `widget_internal_handler` — это внутренний метод `ZenohWidget`. <br> 3. Сохраняет полученные `SubscriptionHandle` для последующей отписки. |
| **3. Обработка Сообщения (`widget_internal_handler`)** | Когда `ZenohClient` доставляет десериализованное сообщение этому `widget_internal_handler`, виджет выполняет следующее для *каждого* `data_source`, связанного с пришедшим топиком: <br> - Извлекает значение из указанного `field` сообщения (`getattr(msg, field)`). <br> - Применяет `multiplier` (если он задан). <br> - Обновляет время последнего получения данных для этого `data_source` (`self.last_update_time`). <br> - Помечает `data_source` как "свежий" (`self._set_data_freshness(src_name, True)`). <br> - Эмитирует Qt-сигнал `self.data_updated.emit(src_name, извлеченное_значение)`. |
| **4. Диспатчеризация в GUI (`_dispatch_update`)** | Слот `_dispatch_update`, подключенный к сигналу `data_updated`, получает `source_name` и `value`. Он пытается вызвать специфический обработчик `handle_<source_name>(value)`. Если такой метод не найден, вызывается общий обработчик `handle_update(source_name, value)` (если он реализован в производном классе). |
| **5. Обновление GUI**                 | Код в `handle_<source_name>()` или `handle_update()` получает уже готовое, обработанное значение и может непосредственно обновлять элементы интерфейса (менять текст, перерисовывать графику и т.д.).                                     |
| **6. Мониторинг Свежести**        | `QTimer` (`freshness_timer`) периодически вызывает `_check_data_freshness()`. Если с момента последнего обновления `last_update_time` для какого-либо `data_source` прошло больше времени, чем `data_timeout`, этот источник помечается как "несвежий" (`_set_data_freshness(src, False)`), что также вызывает сигнал `data_freshness_changed`. |
| **7. Отписка (`unsubscribe()`, `cleanup()`)** | При необходимости (например, при смене `namespace` или уничтожении виджета) вызывается `unsubscribe()`, который использует сохраненные `SubscriptionHandle` для отписки от всех топиков через `ZenohClient`. |

### 3.4 Обработчики

При обработке данных есть два варианта:

* **Глобальный** `handle_update(src, value)` — ловит всё.
* **Специализированный** `handle_<src>(value)` — автодиспатчер вызовет, если такой метод есть.

### 3.5 Абстракция «источник данных»

```jsonc
"pitch": {
  "topic": "level",
  "msg_type": "drill_msgs/msg/Level",
  "field": "pitch",
  "multiplier": 57.3
}
```

`ZenohWidget` сам: подписывается → берёт `msg.pitch` → умножает → шлёт `data_updated("pitch", value)`.

---

## 4. Пример создания виджета

### 1 — конфиг

```jsonc
"angle_indicator": {
  "is_active": true,
  "file_name": "gui_modules",
  "class_name": "AngleIndicator",
  "init_args": {},
  "prep_args": {
    "width": 300,
    "height": 300,
    "data_sources": {
      "pitch": { "topic": "level", "msg_type": "drill_msgs/msg/Level", "field": "pitch" },
      "roll":  { "topic": "level", "msg_type": "drill_msgs/msg/Level", "field": "roll" },
      "mast_angle": { "topic": "tower_state", "msg_type": "drill_msgs/msg/TowerState", "field": "mast_angle" }
    }
  }
}
```

### 2 — базовая реализация класса

```python
from core.base_widget import ZenohWidget
from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QMainWindow

class AngleIndicator(ZenohWidget):
    def __init__(self, core, **kw):
        super().__init__(core, **kw)
        
        # Хранение данных
        self._values = {
            'pitch': 0.0,
            'roll': 0.0,
            'mast_angle': 0.0
        }
        
        # Подключаем сигнал обновления данных
        self.data_updated.connect(self._on_data_updated)
        
    def prepare(self, width=300, height=300, data_sources=None, **kwargs):
        # Вызываем подготовку базового класса
        super().prepare(data_sources=data_sources)
        
        # Создаём интерфейс
        self.frame = QMainWindow(parent=self.core.main_screen)
        # ... создание графических элементов ...
        
    def start(self):
        # Запускаем подписки из базового класса
        super().start()
        
        # Показываем виджет и запускаем таймер
        if self.frame:
            self.frame.show()
            
    def _on_data_updated(self, source_name, value):
        """Обработчик обновления данных от ZenohWidget"""
        # Получаем данные из всех источников
        if source_name in self._values:
            self._values[source_name] = value
            self._update_display()
            
    def _update_display(self):
        """Обновление визуального отображения"""
        # Обновление графического представления с данными из self._values
        pass
        
    def kill(self):
        """Очистка ресурсов"""
        # Останавливаем таймеры
        # ...
        
        # Очищаем подписки из базового класса
        self.cleanup()
```

### 3 — ключевые отличия от простого QWidget

В реальных виджетах часто:

1. **Разделение ответственности:**
   * `AngleIndicator` — логика виджета и управление данными
   * `AngleIndicatorScene` — графическое представление (QGraphicsScene)

2. **Обработка данных:**
   * `_on_data_updated` получает сигналы от ZenohWidget
   * `_values` хранит текущие значения всех источников
   * `_update_display` обновляет визуальное отображение

3. **Жизненный цикл:**
   * `prepare()` — конфигурация и создание UI
   * `start()` — запуск подписок и таймеров
   * `kill()` — правильная очистка ресурсов

Для простых виджетов можно использовать и прямое наследование от QWidget, как показано ниже:

```python
from core.base_widget import ZenohWidget
from PyQt6.QtWidgets import QWidget, QLabel, QVBoxLayout

class SimpleAngleIndicator(ZenohWidget, QWidget):
    def __init__(self, core, **kw):
        QWidget.__init__(self)
        ZenohWidget.__init__(self, core, **kw)
        
        # Создаём простой интерфейс
        layout = QVBoxLayout(self)
        self.pitch_label = QLabel("Вдоль: 0.00°")
        self.roll_label = QLabel("Поперёк: 0.00°")
        layout.addWidget(self.pitch_label)
        layout.addWidget(self.roll_label)
        
    def handle_pitch(self, value):
        self.pitch_label.setText(f"Вдоль: {value:.2f}°")
        
    def handle_roll(self, value):
        self.roll_label.setText(f"Поперёк: {value:.2f}°")
```

*В большинстве случаев менеджер модулей сделает инициализацию за вас.*

---

### TL;DR

* **Client** — один на приложение, экономит трафик.
* **Widget** — описываешь источники → получаешь значения без головной боли.
* Хочешь просто число? — пиши `handle_<source>()`.

Если нужно больше деталей, открой `core/zenoh_client.py` и `core/base_widget.py` — они снабжены комментариями.
