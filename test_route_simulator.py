#!/usr/bin/env python3
"""
ROS2 Route Simulator для тестирования умной логики routes в Map Widget.

Этот скрипт имитирует:
1. Топик driver_status с прогрессом по маршруту
2. Сервис get_current_routes для получения полных маршрутов
3. Топик position с движением по маршруту
4. Различные сценарии (смена action_id, прогресс по точкам)

Использует ROS2 для публикации данных в формате drill_msgs.
"""

import math
from typing import List, Dict, Any
import sys

try:
    import rclpy
    from rclpy.node import Node
    print("ROS2 rclpy imported successfully")
except ImportError:
    print("Error: ROS2 rclpy not found. Please install ROS2 and source the setup files.")
    sys.exit(1)

try:
    from drill_msgs.msg import Position, DriveStatus
    from drill_msgs.srv import GetCurrentRoutes
    from geometry_msgs.msg import Point
    print("drill_msgs imported successfully")
except ImportError:
    print("Error: drill_msgs not found. Please build the drill_msgs package and source the workspace.")
    sys.exit(1)

class ROS2RouteSimulator(Node):
    def __init__(self, namespace: str = "local_sim"):
        """Инициализация ROS2 симулятора."""
        super().__init__('route_simulator')

        self.namespace = namespace

        # Состояние симуляции
        self.current_action_id = 1
        self.current_segment_id = 0
        self.current_point_id = 0
        self.current_position = [0.0, 0.0, 0.0]  # x, y, yaw

        # Создаем тестовые маршруты
        self.test_routes = self.create_test_routes()

        # Флаг для остановки
        self.running = False

        # Создаем publishers
        self.position_pub = self.create_publisher(
            Position,
            f'{namespace}/position',
            10
        )

        self.driver_status_pub = self.create_publisher(
            DriveStatus,
            f'{namespace}/driver_status',
            10
        )

        # Создаем service server
        self.routes_service = self.create_service(
            GetCurrentRoutes,
            f'{namespace}/get_current_routes',
            self.handle_get_routes_request
        )

        # Создаем таймеры
        self.position_timer = self.create_timer(0.1, self.publish_position)  # 10 Hz
        self.status_timer = self.create_timer(1.0, self.publish_driver_status)  # 1 Hz
        self.progress_timer = self.create_timer(3.0, self.update_progress)  # Every 3 seconds

        print(f"ROS2 Route Simulator initialized for namespace '{namespace}'")
        print(f"Created {len(self.test_routes)} test route segments")

    def create_test_routes(self) -> List[Dict[str, Any]]:
        """Создаем тестовые маршруты для демонстрации."""
        routes = []

        # Сегмент 1: Прямая линия на восток
        segment1 = {
            "points": []
        }
        for i in range(10):
            point = {
                "x": float(i * 2),  # Каждые 2 метра
                "y": 0.0,
                "z": 0.0
            }
            segment1["points"].append(point)
        routes.append(segment1)

        # Сегмент 2: Поворот на север
        segment2 = {
            "points": []
        }
        for i in range(8):
            point = {
                "x": 18.0,  # Конец первого сегмента
                "y": float(i * 2),
                "z": 0.0
            }
            segment2["points"].append(point)
        routes.append(segment2)

        # Сегмент 3: Диагональ на северо-запад
        segment3 = {
            "points": []
        }
        for i in range(6):
            point = {
                "x": 18.0 - float(i * 1.5),
                "y": 14.0 + float(i * 1.5),
                "z": 0.0
            }
            segment3["points"].append(point)
        routes.append(segment3)

        return routes

    def start(self):
        """Запуск симулятора."""
        self.running = True
        print("ROS2 Route Simulator started! Press Ctrl+C to stop.")

    def stop(self):
        """Остановка симулятора."""
        self.running = False
        print("ROS2 Route Simulator stopped")

    def publish_position(self):
        """Публикуем текущую позицию робота."""
        if not self.running:
            return

        # Получаем текущую целевую точку
        target_point = self.get_current_target_point()
        if target_point:
            # Плавно движемся к целевой точке
            dx = target_point["x"] - self.current_position[0]
            dy = target_point["y"] - self.current_position[1]

            # Простая логика движения
            speed = 0.1  # м/с
            if abs(dx) > 0.1 or abs(dy) > 0.1:
                distance = math.sqrt(dx*dx + dy*dy)
                self.current_position[0] += (dx / distance) * speed
                self.current_position[1] += (dy / distance) * speed
                self.current_position[2] = math.atan2(dy, dx)  # yaw

        # Создаем ROS2 сообщение позиции
        position_msg = Position()
        position_msg.header.stamp = self.get_clock().now().to_msg()
        position_msg.header.frame_id = "map"
        position_msg.x = self.current_position[0]
        position_msg.y = self.current_position[1]
        position_msg.z = 0.0
        position_msg.yaw = self.current_position[2]
        position_msg.is_reliable = True

        # Публикуем
        self.position_pub.publish(position_msg)

    def publish_driver_status(self):
        """Публикуем статус драйвера."""
        if not self.running:
            return

        # Создаем ROS2 сообщение статуса
        status_msg = DriveStatus()
        status_msg.header.stamp = self.get_clock().now().to_msg()
        status_msg.header.frame_id = "map"
        status_msg.cur_action_id = self.current_action_id
        status_msg.last_action_id = self.current_action_id - 1 if self.current_action_id > 1 else 0
        status_msg.cur_segment_id = self.current_segment_id
        status_msg.cur_point_id = self.current_point_id
        status_msg.status = "executing"

        # Публикуем
        self.driver_status_pub.publish(status_msg)

        # Логирование для отладки
        print(f'Status: action_id={status_msg.cur_action_id}, '
              f'segment={status_msg.cur_segment_id}, point={status_msg.cur_point_id}')

    def update_progress(self):
        """Обновляем прогресс движения по маршруту."""
        # Проверяем достигли ли мы текущей точки
        target_point = self.get_current_target_point()
        if target_point:
            dx = target_point["x"] - self.current_position[0]
            dy = target_point["y"] - self.current_position[1]
            distance = math.sqrt(dx*dx + dy*dy)

            # Если близко к точке, переходим к следующей
            if distance < 0.5:  # 50 см
                self.advance_to_next_point()

    def get_current_target_point(self) -> Dict[str, float]:
        """Получаем текущую целевую точку."""
        if (self.current_segment_id < len(self.test_routes) and
            self.current_point_id < len(self.test_routes[self.current_segment_id]["points"])):
            return self.test_routes[self.current_segment_id]["points"][self.current_point_id]
        return None

    def advance_to_next_point(self):
        """Переходим к следующей точке маршрута."""
        current_segment = self.test_routes[self.current_segment_id]

        # Переходим к следующей точке в текущем сегменте
        if self.current_point_id < len(current_segment["points"]) - 1:
            self.current_point_id += 1
            print(f'Advanced to point {self.current_point_id}')

        # Переходим к следующему сегменту
        elif self.current_segment_id < len(self.test_routes) - 1:
            self.current_segment_id += 1
            self.current_point_id = 0
            print(f'Advanced to segment {self.current_segment_id}')

        # Завершили все сегменты - начинаем новый action
        else:
            self.current_action_id += 1
            self.current_segment_id = 0
            self.current_point_id = 0
            print(f'Started new action {self.current_action_id}')

            # Сбрасываем позицию для нового маршрута
            self.current_position = [0.0, 0.0, 0.0]

    def handle_get_routes_request(self, _request, response):
        """Обработчик запросов сервиса get_current_routes."""
        print(f'Service called: get_current_routes for action_id={self.current_action_id}')

        # Заполняем response
        response.header.stamp = self.get_clock().now().to_msg()
        response.header.frame_id = "map"
        response.action_id = self.current_action_id
        response.success = True
        response.message = f"Returned {len(self.test_routes)} path segments"

        # Конвертируем тестовые маршруты в ROS2 формат
        for i, route_data in enumerate(self.test_routes):
            # Создаем PathSegment (предполагаем, что это часть GetCurrentRoutes.Response)
            # Здесь нужно будет адаптировать под реальную структуру сообщения
            segment = {}  # Placeholder - нужно использовать реальный тип PathSegment
            segment['segment_id'] = i
            segment['points'] = []

            for point_data in route_data["points"]:
                point = Point()
                point.x = point_data["x"]
                point.y = point_data["y"]
                point.z = point_data["z"]
                segment['points'].append(point)

            # response.path_segments.append(segment)  # Раскомментировать когда будет реальная структура

        print(f'Service response: {len(self.test_routes)} segments')
        return response


def main():
    """Главная функция."""
    # Инициализируем ROS2
    rclpy.init()

    # Получаем namespace из аргументов командной строки
    namespace = "local_sim"
    if len(sys.argv) > 1:
        namespace = sys.argv[1]

    # Создаем и запускаем симулятор
    simulator = ROS2RouteSimulator(namespace)
    simulator.start()

    try:
        # Запускаем ROS2 spin
        rclpy.spin(simulator)
    except KeyboardInterrupt:
        print("\nStopping simulator...")
    finally:
        simulator.stop()
        simulator.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
